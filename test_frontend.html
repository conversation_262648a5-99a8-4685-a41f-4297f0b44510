<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收费内容功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
        }
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .code-block {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>收费内容功能测试页面</h1>
    
    <div class="test-section">
        <h2 class="test-title">1. 数据库准备</h2>
        <p>首先确保已经执行了数据库更新：</p>
        <div class="code-block">
            mysql -u用户名 -p数据库名 &lt; vip_content_backend.sql
        </div>
        <p>或者运行测试脚本创建测试数据：</p>
        <a href="/test_paid_content.php" class="test-link" target="_blank">运行测试脚本</a>
    </div>

    <div class="test-section">
        <h2 class="test-title">2. 后台管理测试</h2>
        <p>测试后台编辑收费内容功能：</p>
        <a href="/admin/posts/add" class="test-link" target="_blank">添加收费帖子</a>
        <a href="/admin/posts" class="test-link" target="_blank">管理帖子列表</a>
        
        <h3>测试步骤：</h3>
        <ol>
            <li>在内容中使用 <code>[SPLIT]</code> 分段</li>
            <li>选择收费段落（如"第3段内容收费"）</li>
            <li>设置价格（如50积分）</li>
            <li>保存帖子</li>
        </ol>
    </div>

    <div class="test-section">
        <h2 class="test-title">3. 前端显示测试</h2>
        <p>测试前端帖子详情页面的收费内容显示：</p>
        <div id="postLinks">
            <p>正在获取测试帖子...</p>
        </div>
        
        <h3>测试要点：</h3>
        <ul>
            <li>未登录用户：看到收费内容的购买提示</li>
            <li>已登录但未购买：看到购买按钮</li>
            <li>已购买用户：看到完整内容</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">4. API接口测试</h2>
        <p>测试API接口是否正常工作：</p>
        <div id="apiTest">
            <button onclick="testAPI()" class="test-link">测试API接口</button>
            <div id="apiResult"></div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">5. 功能状态检查</h2>
        <div id="statusCheck">
            <p>正在检查功能状态...</p>
        </div>
    </div>

    <script>
        // 页面加载时检查功能状态
        document.addEventListener('DOMContentLoaded', function() {
            checkFunctionStatus();
            loadTestPosts();
        });

        // 检查功能状态
        async function checkFunctionStatus() {
            const statusDiv = document.getElementById('statusCheck');
            let html = '<h3>功能状态：</h3>';

            try {
                // 检查API接口
                const response = await fetch('/api/posts?limit=1');
                if (response.ok) {
                    html += '<div class="status success">✅ API接口正常</div>';
                } else {
                    html += '<div class="status error">❌ API接口异常</div>';
                }
            } catch (error) {
                html += '<div class="status error">❌ API接口连接失败</div>';
            }

            // 检查路由
            try {
                const response = await fetch('/index/index/post_detail?id=1');
                if (response.ok) {
                    html += '<div class="status success">✅ 前端路由正常</div>';
                } else {
                    html += '<div class="status warning">⚠️ 前端路由可能有问题</div>';
                }
            } catch (error) {
                html += '<div class="status error">❌ 前端路由连接失败</div>';
            }

            statusDiv.innerHTML = html;
        }

        // 加载测试帖子
        async function loadTestPosts() {
            try {
                const response = await fetch('/api/posts?limit=5');
                const result = await response.json();
                
                if (result.code === 1 && result.data.list.length > 0) {
                    let html = '<p>可用的测试帖子：</p>';
                    result.data.list.forEach(post => {
                        html += `<a href="/index/index/post_detail?id=${post.id}" class="test-link" target="_blank">${post.title}</a>`;
                    });
                    document.getElementById('postLinks').innerHTML = html;
                } else {
                    document.getElementById('postLinks').innerHTML = '<p class="status warning">⚠️ 没有找到帖子，请先创建测试数据</p>';
                }
            } catch (error) {
                document.getElementById('postLinks').innerHTML = '<p class="status error">❌ 无法加载帖子列表</p>';
            }
        }

        // 测试API接口
        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<p>正在测试...</p>';

            try {
                // 测试帖子列表API
                const listResponse = await fetch('/api/posts?limit=1');
                const listResult = await listResponse.json();
                
                if (listResult.code === 1 && listResult.data.list.length > 0) {
                    const postId = listResult.data.list[0].id;
                    
                    // 测试帖子详情API
                    const detailResponse = await fetch(`/api/posts/detail?id=${postId}`);
                    const detailResult = await detailResponse.json();
                    
                    if (detailResult.code === 1) {
                        let html = '<div class="status success">✅ API测试成功</div>';
                        html += '<div class="code-block">';
                        html += `帖子ID: ${detailResult.data.id}<br>`;
                        html += `标题: ${detailResult.data.title}<br>`;
                        html += `是否有收费内容: ${detailResult.data.has_paid_content ? '是' : '否'}<br>`;
                        if (detailResult.data.paid_content_info) {
                            html += `收费段落: 第${detailResult.data.paid_content_info.index}段<br>`;
                            html += `收费价格: ${detailResult.data.paid_content_info.price}积分<br>`;
                        }
                        html += '</div>';
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = '<div class="status error">❌ 详情API返回错误</div>';
                    }
                } else {
                    resultDiv.innerHTML = '<div class="status error">❌ 列表API返回错误</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="status error">❌ API测试失败: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
