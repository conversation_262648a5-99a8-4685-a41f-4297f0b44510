<?php

namespace app\common\model;

use think\Model;

/**
 * 帖子购买记录模型
 */
class PostPurchase extends Model
{
    // 表名
    protected $name = 'post_purchases';
    
    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    /**
     * 检查用户是否已购买某帖子
     * @param int $userId 用户ID
     * @param int $postId 帖子ID
     * @return bool
     */
    public static function hasPurchased($userId, $postId)
    {
        return self::where([
            'user_id' => $userId,
            'post_id' => $postId,
            'status' => 1
        ])->find() ? true : false;
    }

    /**
     * 创建购买记录
     * @param int $userId 用户ID
     * @param int $postId 帖子ID
     * @param int $price 价格(积分)
     * @return bool
     */
    public static function createPurchase($userId, $postId, $price)
    {
        try {
            self::create([
                'user_id' => $userId,
                'post_id' => $postId,
                'price' => $price,
                'status' => 1
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id');
    }

    /**
     * 关联帖子模型
     */
    public function post()
    {
        return $this->belongsTo('Post', 'post_id');
    }

    /**
     * 获取帖子的购买人数
     * @param int $postId 帖子ID
     * @return int
     */
    public static function getPurchaseCount($postId)
    {
        return self::where([
            'post_id' => $postId,
            'status' => 1
        ])->count();
    }
}
