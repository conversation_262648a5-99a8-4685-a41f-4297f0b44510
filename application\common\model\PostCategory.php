<?php

namespace app\common\model;

use think\Model;

/**
 * 帖子分类模型
 */
class PostCategory extends Model
{
    // 表名
    protected $name = 'post_categories';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    /**
     * 关联帖子
     */
    public function posts()
    {
        return $this->hasMany('Post', 'category_id');
    }

    /**
     * 获取启用的分类
     */
    public static function getEnabledCategories()
    {
        return self::where('status', 1)
                   ->order('sort_order', 'asc')
                   ->order('id', 'asc')
                   ->select();
    }
}
