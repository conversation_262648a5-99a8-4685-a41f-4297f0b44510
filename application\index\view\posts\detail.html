<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$post.title} - {$site.title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: #007bff;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
        }

        .back-btn {
            color: white;
            text-decoration: none;
            font-size: 14px;
        }

        .post-container {
            padding: 20px;
        }

        .post-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }

        .post-title {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .post-icon {
            width: 32px;
            height: 32px;
            margin-right: 10px;
        }

        .post-title h1 {
            font-size: 20px;
            color: #333;
            flex: 1;
        }

        .post-meta {
            display: flex;
            align-items: center;
            color: #666;
            font-size: 14px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .post-author {
            display: flex;
            align-items: center;
        }

        .author-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .post-stats {
            display: flex;
            gap: 15px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .post-content {
            font-size: 16px;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            margin: 10px 0;
        }

        .post-actions {
            display: flex;
            gap: 10px;
            padding: 20px 0;
            border-top: 1px solid #eee;
        }

        .action-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            color: #666;
            text-decoration: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            border-color: #007bff;
            color: #007bff;
        }

        .action-btn.liked {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #f56565;
        }

        /* 收费内容样式 */
        .paid-content-notice {
            background: #fff3cd !important;
            border-left: 4px solid #ffc107 !important;
            padding: 12px 15px !important;
            margin: 10px 0 !important;
            font-size: 13px !important;
            color: #856404 !important;
            border-radius: 0 4px 4px 0 !important;
        }

        .paid-content-notice button {
            background: #ffc107 !important;
            border: none !important;
            padding: 6px 16px !important;
            border-radius: 3px !important;
            color: #212529 !important;
            cursor: pointer !important;
            font-size: 12px !important;
            font-weight: 500 !important;
            transition: background-color 0.3s ease !important;
            margin-top: 5px !important;
        }

        .paid-content-notice button:hover {
            background: #e0a800 !important;
        }

        /* 手机端优化 */
        @media (max-width: 768px) {
            .container {
                margin: 0;
                box-shadow: none;
            }

            .post-container {
                padding: 15px;
            }

            .post-title h1 {
                font-size: 18px;
            }

            .post-meta {
                font-size: 13px;
            }

            .post-content {
                font-size: 15px;
            }

            .post-actions {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>帖子详情</h1>
            <a href="/" class="back-btn">返回首页</a>
        </div>

        <div class="post-container">
            <div id="loading" class="loading">
                加载中...
            </div>

            <div id="error" class="error" style="display: none;">
                加载失败，请刷新重试
            </div>

            <div id="postContent" style="display: none;">
                <!-- 帖子内容将通过JavaScript加载 -->
            </div>
        </div>
    </div>

    <script>
        let postId = null;
        let isLiked = false;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL获取帖子ID
            const urlParams = new URLSearchParams(window.location.search);
            postId = urlParams.get('id');
            
            if (postId) {
                loadPostDetail();
            } else {
                showError('参数错误');
            }
        });

        // 加载帖子详情
        async function loadPostDetail() {
            try {
                const response = await fetch(`/api/posts/detail?id=${postId}`);
                const result = await response.json();
                
                if (result.code === 1) {
                    renderPost(result.data);
                } else {
                    showError(result.msg || '帖子不存在');
                }
            } catch (error) {
                console.error('加载帖子失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 渲染帖子内容
        function renderPost(post) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('postContent').style.display = 'block';
            
            // 更新页面标题
            document.title = post.title + ' - ' + (window.siteTitle || '论坛');
            
            const postContent = document.getElementById('postContent');
            postContent.innerHTML = `
                <div class="post-header">
                    <div class="post-title">
                        <img src="${post.icon_url}" alt="图标" class="post-icon">
                        <h1>${post.title}</h1>
                    </div>
                    <div class="post-meta">
                        <div class="post-author">
                            <img src="${post.user.avatar || '/images/default-avatar.png'}" alt="头像" class="author-avatar">
                            <span>${post.user.nickname || post.user.username}</span>
                        </div>
                        <span>发布于 ${formatDate(post.createtime)}</span>
                        <span class="post-type">${post.post_type_text}</span>
                        <span>分类：${post.category.name}</span>
                    </div>
                    <div class="post-stats">
                        <div class="stat-item">
                            <span>👁</span>
                            <span>${post.views}</span>
                        </div>
                        <div class="stat-item">
                            <span>💬</span>
                            <span>${post.replies}</span>
                        </div>
                        <div class="stat-item">
                            <span>👍</span>
                            <span id="likeCount">${post.likes}</span>
                        </div>
                    </div>
                </div>
                
                <div class="post-content">
                    ${post.content}
                </div>
                
                <div class="post-actions">
                    <button class="action-btn" id="likeBtn" onclick="toggleLike()">
                        <span>👍</span> 点赞
                    </button>
                    <button class="action-btn" onclick="showReplyForm()">
                        <span>💬</span> 回复
                    </button>
                    <button class="action-btn" onclick="sharePost()">
                        <span>📤</span> 分享
                    </button>
                </div>
            `;
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').textContent = message;
        }

        // 格式化日期
        function formatDate(timestamp) {
            // 如果是时间戳（数字），转换为毫秒
            const date = new Date(typeof timestamp === 'number' ? timestamp * 1000 : timestamp);
            const now = new Date();
            const diff = now - date;

            if (diff < 60000) { // 1分钟内
                return '刚刚';
            } else if (diff < 3600000) { // 1小时内
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) { // 1天内
                return Math.floor(diff / 3600000) + '小时前';
            } else {
                return date.toLocaleDateString();
            }
        }

        // 切换点赞状态
        function toggleLike() {
            // 这里可以添加点赞API调用
            const likeBtn = document.getElementById('likeBtn');
            const likeCount = document.getElementById('likeCount');
            
            if (isLiked) {
                likeBtn.classList.remove('liked');
                likeBtn.innerHTML = '<span>👍</span> 点赞';
                likeCount.textContent = parseInt(likeCount.textContent) - 1;
                isLiked = false;
            } else {
                likeBtn.classList.add('liked');
                likeBtn.innerHTML = '<span>👍</span> 已赞';
                likeCount.textContent = parseInt(likeCount.textContent) + 1;
                isLiked = true;
            }
        }

        // 显示回复表单
        function showReplyForm() {
            alert('回复功能开发中...');
        }

        // 分享帖子
        function sharePost() {
            if (navigator.share) {
                navigator.share({
                    title: document.title,
                    url: window.location.href
                });
            } else {
                // 复制链接到剪贴板
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('链接已复制到剪贴板');
                });
            }
        }

        // 购买收费内容
        async function purchaseContent(postId, price) {
            if (!confirm(`确定要花费 ${price} 金币购买此内容吗？`)) {
                return;
            }

            try {
                const response = await fetch('/api/posts/purchase', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        post_id: postId,
                        price: price
                    })
                });

                const result = await response.json();

                if (result.code === 1) {
                    alert('购买成功！');
                    // 重新加载帖子内容
                    loadPostDetail();
                } else {
                    alert(result.msg || '购买失败');
                }
            } catch (error) {
                console.error('购买失败:', error);
                alert('网络错误，请稍后重试');
            }
        }
    </script>
</body>
</html>
