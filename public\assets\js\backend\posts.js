define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数
            Table.api.init({
                extend: {
                    index_url: 'posts/index' + location.search,
                    add_url: 'posts/add',
                    edit_url: 'posts/edit',
                    del_url: 'posts/del',
                    multi_url: 'posts/multi',
                    import_url: 'posts/import',
                    table: 'posts',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: 'ID', operate: false},
                        {field: 'title', title: '标题', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'user.username', title: '作者', operate: 'LIKE'},
                        {field: 'category.name', title: '分类', operate: 'LIKE'},
                        {field: 'post_type_text', title: '帖子类型', operate: false, searchList: {"normal":"普通帖","sale":"出售帖","hot":"热门帖","expert":"高手帖"}},
                        {field: 'paid_content_price', title: '收费内容', operate: false, formatter: function(value, row, index) {
                            if (row.paid_content_index > 0 && row.paid_content_price > 0) {
                                return '<span class="label label-warning">第' + row.paid_content_index + '段 ' + row.paid_content_price + '金币</span>';
                            }
                            return '<span class="label label-success">免费</span>';
                        }},
                        {field: 'icon_url', title: '图标', operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'views', title: '浏览数', operate: false},
                        {field: 'replies', title: '回复数', operate: false},
                        {field: 'likes', title: '点赞数', operate: false},
                        {field: 'is_top', title: '置顶', searchList: {"0":"否","1":"是"}, formatter: Table.api.formatter.toggle},
                        {field: 'is_hot', title: '热门', searchList: {"0":"否","1":"是"}, formatter: Table.api.formatter.toggle},
                        {field: 'status_text', title: '状态', operate: false, searchList: {"published":"已发布","draft":"草稿","hidden":"隐藏","deleted":"已删除"}},
                        {field: 'createtime', title: '创建时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: '操作', table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
                
                // 图标类型切换
                $(document).on('change', 'input[name="row[icon_type]"]', function() {
                    var iconType = $(this).val();
                    if (iconType === 'builtin') {
                        $('#builtin-icons').show();
                        $('#upload-icon').hide();
                    } else {
                        $('#builtin-icons').hide();
                        $('#upload-icon').show();
                    }
                });
                
                // 内置图标选择
                $(document).on('click', '.icon-item', function() {
                    $('.icon-item').removeClass('selected');
                    $(this).addClass('selected');
                    var iconKey = $(this).data('value');
                    $('input[name="row[selected_icon]"]').remove();
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'row[selected_icon]',
                        value: iconKey
                    }).appendTo('form');
                });
            }
        }
    };
    return Controller;
});
