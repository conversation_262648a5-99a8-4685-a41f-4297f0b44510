-- ----------------------------
-- Table structure for fa_navigation
-- ----------------------------
CREATE TABLE `fa_navigation` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '导航标题',
  `image` varchar(255) NOT NULL DEFAULT '' COMMENT '导航图片',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '链接地址',
  `target` varchar(20) NOT NULL DEFAULT '_self' COMMENT '打开方式',
  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `sort` (`sort`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='导航链接表';

-- ----------------------------
-- Records of fa_navigation
-- ----------------------------
INSERT INTO `fa_navigation` VALUES ('1', '首页', '/assets/img/nav/home.png', '/', '_self', '1', 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
INSERT INTO `fa_navigation` VALUES ('2', '关于我们', '/assets/img/nav/about.png', '/about', '_self', '2', 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
INSERT INTO `fa_navigation` VALUES ('3', '联系我们', '/assets/img/nav/contact.png', '/contact', '_self', '3', 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
INSERT INTO `fa_navigation` VALUES ('4', '产品中心', '/assets/img/nav/products.png', '/products', '_self', '4', 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
