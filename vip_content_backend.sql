-- =====================================================
-- VIP内容功能后端数据库结构
-- =====================================================

-- 1. 给帖子表添加VIP相关字段
ALTER TABLE `fa_posts` ADD COLUMN `has_vip_content` tinyint(1) DEFAULT 0 COMMENT '是否包含收费内容';
ALTER TABLE `fa_posts` ADD COLUMN `vip_price` int(10) DEFAULT 0 COMMENT '收费内容价格(积分)';

-- 2. 添加分段收费内容字段
ALTER TABLE `fa_posts` ADD COLUMN `paid_content_index` int(10) DEFAULT 0 COMMENT '收费内容段落索引(从1开始,0表示无收费内容)';
ALTER TABLE `fa_posts` ADD COLUMN `paid_content_price` int(10) DEFAULT 0 COMMENT '收费内容价格(积分)';

-- 2. 创建帖子购买记录表
CREATE TABLE `fa_post_purchases` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `post_id` int(10) unsigned NOT NULL COMMENT '帖子ID',
  `price` int(10) NOT NULL COMMENT '支付积分',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1已支付',
  `createtime` int(10) DEFAULT NULL COMMENT '购买时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_post` (`user_id`,`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帖子购买记录表';

-- 执行完成提示
SELECT '✅ VIP内容数据库结构创建完成！' as '执行结果';
SELECT '📋 已添加has_vip_content和vip_price字段到fa_posts表' as '帖子表更新';
SELECT '📋 已创建fa_post_purchases购买记录表' as '购买记录表';
