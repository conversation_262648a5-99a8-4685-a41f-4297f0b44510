<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">标题:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" class="form-control" name="row[title]" type="text" data-rule="required">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">分类:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-category_id" class="form-control selectpicker" name="row[category_id]" data-rule="required">
                <option value="">请选择分类</option>
                {volist name="categoryList" id="category"}
                <option value="{$category.id}">{$category.name}</option>
                {/volist}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">帖子类型:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-post_type" class="form-control selectpicker" name="row[post_type]">
                {volist name="postTypes" id="typeName" key="typeKey"}
                <option value="{$typeKey}" {if condition="$typeKey == 'normal'"}selected{/if}>{$typeName}</option>
                {/volist}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">图标设置:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label><input type="radio" name="row[icon_type]" value="category" checked> 使用分类图标</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[icon_type]" value="upload"> 上传自定义图标</label>
            </div>
        </div>
    </div>
    <div class="form-group" id="category-icon-info">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <div class="alert alert-info">
                <i class="fa fa-info-circle"></i> 帖子将自动使用所选分类的图标
            </div>
        </div>
    </div>
    <div class="form-group" id="upload-icon" style="display: none;">
        <label class="control-label col-xs-12 col-sm-2">上传自定义图标:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-icon_path" class="form-control" size="50" name="row[icon_path]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-icon" class="btn btn-danger plupload" data-input-id="c-icon_path" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-icon"><i class="fa fa-upload"></i> 上传</button></span>
                </div>
                <span class="msg-box n-right" for="c-icon_path"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-icon"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">内容:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" class="form-control editor" rows="5" name="row[content]" cols="50" data-rule="required"></textarea>
            <div class="help-block">
                <small class="text-muted">提示：如需设置收费内容，请在不同段落之间使用 <code>[SPLIT]</code> 分隔，然后在下方选择收费段落</small>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">收费设置:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="row">
                <div class="col-sm-6">
                    <select id="c-paid_content_index" name="row[paid_content_index]" class="form-control">
                        <option value="0">无收费内容</option>
                        <option value="1">第1段内容收费</option>
                        <option value="2">第2段内容收费</option>
                        <option value="3">第3段内容收费</option>
                        <option value="4">第4段内容收费</option>
                        <option value="5">第5段内容收费</option>
                        <option value="6">第6段内容收费</option>
                        <option value="7">第7段内容收费</option>
                        <option value="8">第8段内容收费</option>
                        <option value="9">第9段内容收费</option>
                        <option value="10">第10段内容收费</option>
                    </select>
                </div>
                <div class="col-sm-6">
                    <div class="input-group">
                        <input type="number" id="c-paid_content_price" name="row[paid_content_price]" class="form-control" placeholder="收费价格" min="0" value="0" disabled>
                        <span class="input-group-addon">积分</span>
                    </div>
                </div>
            </div>
            <div class="help-block">
                <small class="text-muted">选择需要收费的段落序号，并设置价格（积分）</small>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">状态:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label><input type="radio" name="row[status]" value="published" checked> 已发布</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[status]" value="draft"> 草稿</label>
            </div>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">确定</button>
            <button type="reset" class="btn btn-default btn-embossed">重置</button>
        </div>
    </div>
</form>

<style>
.icon-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.icon-item {
    text-align: center;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    min-width: 80px;
}
.icon-item:hover {
    border-color: #007bff;
}
.icon-item.selected {
    border-color: #007bff;
    background-color: #f0f8ff;
}
</style>

<script>
$(function() {
    // 图标类型切换
    $('input[name="row[icon_type]"]').change(function() {
        if ($(this).val() === 'category') {
            $('#category-icon-info').show();
            $('#upload-icon').hide();
        } else {
            $('#category-icon-info').hide();
            $('#upload-icon').show();
        }
    });

    // 收费内容设置交互
    $('#c-paid_content_index').change(function() {
        var selectedIndex = $(this).val();
        var priceInput = $('#c-paid_content_price');

        if (selectedIndex == '0') {
            // 无收费内容
            priceInput.prop('disabled', true).val(0);
        } else {
            // 有收费内容
            priceInput.prop('disabled', false);
            if (priceInput.val() == '0') {
                priceInput.val(''); // 清空默认值，提示用户输入
            }
        }
    });

    // 初始化状态
    $('#c-paid_content_index').trigger('change');
});
</script>
