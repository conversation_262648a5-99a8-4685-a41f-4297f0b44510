-- 导航表字段更新脚本
-- 将icon字段改为image字段

-- 如果表已存在，先备份数据
CREATE TABLE IF NOT EXISTS `fa_navigation_backup` AS SELECT * FROM `fa_navigation`;

-- 添加新的image字段
ALTER TABLE `fa_navigation` ADD COLUMN `image` varchar(255) NOT NULL DEFAULT '' COMMENT '导航图片' AFTER `title`;

-- 如果有icon字段的数据，可以手动迁移或清空
-- UPDATE `fa_navigation` SET `image` = '/assets/img/nav/default.png' WHERE `icon` != '';

-- 删除旧的icon字段（如果存在）
-- ALTER TABLE `fa_navigation` DROP COLUMN IF EXISTS `icon`;

-- 更新示例数据
UPDATE `fa_navigation` SET `image` = '/assets/img/nav/home.png' WHERE `id` = 1;
UPDATE `fa_navigation` SET `image` = '/assets/img/nav/about.png' WHERE `id` = 2;
UPDATE `fa_navigation` SET `image` = '/assets/img/nav/contact.png' WHERE `id` = 3;
UPDATE `fa_navigation` SET `image` = '/assets/img/nav/products.png' WHERE `id` = 4;
