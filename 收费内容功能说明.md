# 帖子收费内容功能说明

## 功能概述

现在可以在帖子中设置某一段内容为收费内容，用户需要支付积分才能查看完整内容。

## 使用方法

### 1. 后台发布收费帖子

1. **编写内容**：在内容编辑器中正常编写帖子内容
2. **分段标记**：在需要分段的地方使用 `[SPLIT]` 标记
   ```
   这是第一段免费内容...
   [SPLIT]
   这是第二段免费内容...
   [SPLIT]
   这是第三段收费内容...
   [SPLIT]
   这是第四段免费内容...
   ```

3. **设置收费**：
   - 在"收费设置"中选择需要收费的段落（如"第3段内容收费"）
   - 设置收费价格（积分数量）

4. **保存发布**：点击确定保存帖子

### 2. 前端用户体验

#### 未购买用户看到：
- 免费段落正常显示
- 收费段落显示为：
  ```
  🔒
  此段内容需要支付 XX 积分查看
  [立即购买]
  ```

#### 已购买用户看到：
- 所有内容正常显示，包括收费段落的完整内容

### 3. 购买流程

1. 用户点击"立即购买"按钮
2. 系统弹出确认对话框："确定要花费 XX 积分购买此内容吗？"
3. 用户确认后：
   - 扣除用户积分
   - 创建购买记录
   - 自动刷新页面显示完整内容

## 安装步骤

### 1. 数据库变更

执行SQL文件来添加新字段：
```bash
# 方法1：直接执行SQL文件
mysql -u用户名 -p数据库名 < vip_content_backend.sql

# 方法2：或者运行迁移文件
php think migrate:run
```

### 2. 测试功能

运行测试脚本验证功能：
```bash
# 在浏览器中访问
http://你的域名/test_paid_content.php
```

### 3. 前端测试

使用测试页面验证前端功能：
```bash
# 在浏览器中访问
http://你的域名/test_frontend.html
```

### 4. 完整测试流程

1. **后台测试**：
   - 访问 `/admin/posts/add` 创建收费帖子
   - 在内容中使用 `[SPLIT]` 分段
   - 设置收费段落和价格
   - 保存帖子

2. **前端测试**：
   - 访问 `/index/index/post_detail?id=帖子ID`
   - 验证收费内容显示为购买提示
   - 测试购买功能（需要登录）

3. **API测试**：
   - 访问 `/api/posts/detail?id=帖子ID`
   - 检查返回的收费内容信息

### 5. 清理测试数据（可选）

测试完成后可以删除测试数据：
```sql
DELETE FROM fa_posts WHERE title = '测试收费内容帖子';
DELETE FROM fa_post_categories WHERE name = '测试分类';
DELETE FROM fa_user WHERE username = 'testuser';
DELETE FROM fa_post_purchases WHERE post_id NOT IN (SELECT id FROM fa_posts);
```

## API接口

### 获取帖子详情
- **接口**：`GET /api/posts/detail?id={post_id}`
- **返回**：包含处理后的content字段，收费内容会根据用户购买情况显示

### 购买收费内容
- **接口**：`POST /api/posts/purchase`
- **参数**：
  - `post_id`: 帖子ID
  - `price`: 支付价格
- **需要登录**：是

## 注意事项

1. **段落索引**：从1开始计数，0表示无收费内容
2. **内容分割**：使用 `[SPLIT]` 标记分段，大小写敏感
3. **积分检查**：购买前会检查用户积分是否足够
4. **重复购买**：已购买用户不能重复购买同一内容
5. **价格验证**：购买时会验证价格是否与设置的价格一致

## 示例

### 后台编辑示例：
```
网络配置基础知识，包括以下几个方面：

1. 基本概念和术语
[SPLIT]
2. 网络拓扑结构设计
[SPLIT]
3. 高级路由配置命令
这里是核心的配置命令和参数说明...
[SPLIT]
4. 故障排除方法
```

设置：第3段内容收费，价格50积分

### 前端显示效果：
- 第1段：正常显示
- 第2段：正常显示  
- 第3段：显示购买提示（未购买）或完整内容（已购买）
- 第4段：正常显示
