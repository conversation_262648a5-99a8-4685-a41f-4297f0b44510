<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreatePostCategoriesTable extends Migrator
{
    /**
     * 创建帖子分类表
     */
    public function change()
    {
        $table = $this->table('post_categories', ['id' => false, 'primary_key' => ['id']]);
        $table->addColumn('id', 'integer', ['identity' => true, 'signed' => false, 'comment' => '分类ID'])
              ->addColumn('name', 'string', ['limit' => 100, 'comment' => '分类名称'])
              ->addColumn('description', 'string', ['limit' => 255, 'null' => true, 'comment' => '分类描述'])
              ->addColumn('icon', 'string', ['limit' => 255, 'null' => true, 'comment' => '分类图标'])
              ->addColumn('sort_order', 'integer', ['default' => 0, 'comment' => '排序'])
              ->addColumn('status', 'boolean', ['default' => true, 'comment' => '状态'])
              ->addColumn('create_time', 'datetime', ['comment' => '创建时间'])
              ->addColumn('update_time', 'datetime', ['comment' => '更新时间'])
              ->addIndex(['status'])
              ->addIndex(['sort_order'])
              ->create();
    }
}
