<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API响应测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .json-display {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>API响应测试</h1>
    
    <div class="test-section">
        <h2>测试帖子详情API</h2>
        <p>输入帖子ID来测试API响应：</p>
        <input type="number" id="postId" placeholder="输入帖子ID" value="1" style="padding: 8px; margin-right: 10px;">
        <button onclick="testPostDetail()">测试详情API</button>
        <button onclick="testPostList()">测试列表API</button>
        
        <div id="apiStatus"></div>
        <div id="apiResponse" class="json-display"></div>
    </div>

    <div class="test-section">
        <h2>购买信息检查</h2>
        <div id="purchaseInfo"></div>
    </div>

    <script>
        async function testPostDetail() {
            const postId = document.getElementById('postId').value;
            const statusDiv = document.getElementById('apiStatus');
            const responseDiv = document.getElementById('apiResponse');
            const purchaseDiv = document.getElementById('purchaseInfo');
            
            if (!postId) {
                statusDiv.innerHTML = '<div class="status error">请输入帖子ID</div>';
                return;
            }

            try {
                statusDiv.innerHTML = '<div class="status">正在请求API...</div>';
                
                const response = await fetch(`/api/posts/detail?id=${postId}`);
                const result = await response.json();
                
                if (result.code === 1) {
                    statusDiv.innerHTML = '<div class="status success">✅ API请求成功</div>';
                    responseDiv.textContent = JSON.stringify(result, null, 2);
                    
                    // 检查购买信息
                    const post = result.data;
                    let purchaseHtml = '<h3>购买信息分析：</h3>';
                    
                    if (post.paid_content_index > 0) {
                        purchaseHtml += `<p>✅ 有收费内容：第${post.paid_content_index}段，价格${post.paid_content_price}金币</p>`;
                        
                        if (post.purchase_info) {
                            purchaseHtml += `<p>✅ 购买信息存在：</p>`;
                            purchaseHtml += `<ul>`;
                            purchaseHtml += `<li>价格：${post.purchase_info.price}金币</li>`;
                            purchaseHtml += `<li>购买人数：${post.purchase_info.purchase_count}人</li>`;
                            purchaseHtml += `<li>帖子ID：${post.purchase_info.post_id}</li>`;
                            purchaseHtml += `</ul>`;
                            purchaseHtml += `<p style="color: green;">✅ 前端应该会显示购买区域</p>`;
                        } else {
                            purchaseHtml += `<p style="color: red;">❌ 购买信息不存在（可能已购买或API逻辑有问题）</p>`;
                        }
                    } else {
                        purchaseHtml += `<p>ℹ️ 这是免费帖子，无收费内容</p>`;
                    }
                    
                    purchaseDiv.innerHTML = purchaseHtml;
                } else {
                    statusDiv.innerHTML = '<div class="status error">❌ API返回错误：' + (result.msg || '未知错误') + '</div>';
                    responseDiv.textContent = JSON.stringify(result, null, 2);
                    purchaseDiv.innerHTML = '';
                }
            } catch (error) {
                statusDiv.innerHTML = '<div class="status error">❌ 网络错误：' + error.message + '</div>';
                responseDiv.textContent = '请求失败';
                purchaseDiv.innerHTML = '';
            }
        }

        async function testPostList() {
            const statusDiv = document.getElementById('apiStatus');
            const responseDiv = document.getElementById('apiResponse');
            const purchaseDiv = document.getElementById('purchaseInfo');
            
            try {
                statusDiv.innerHTML = '<div class="status">正在请求列表API...</div>';
                
                const response = await fetch('/api/posts?limit=5');
                const result = await response.json();
                
                if (result.code === 1) {
                    statusDiv.innerHTML = '<div class="status success">✅ 列表API请求成功</div>';
                    responseDiv.textContent = JSON.stringify(result, null, 2);
                    
                    // 分析列表中的收费帖子
                    const posts = result.data.list;
                    let listHtml = '<h3>列表中的收费帖子：</h3>';
                    
                    const paidPosts = posts.filter(post => post.paid_content_index > 0);
                    if (paidPosts.length > 0) {
                        listHtml += '<ul>';
                        paidPosts.forEach(post => {
                            listHtml += `<li>ID:${post.id} - ${post.title} (第${post.paid_content_index}段，${post.paid_content_price}金币)</li>`;
                        });
                        listHtml += '</ul>';
                    } else {
                        listHtml += '<p>当前列表中没有收费帖子</p>';
                    }
                    
                    purchaseDiv.innerHTML = listHtml;
                } else {
                    statusDiv.innerHTML = '<div class="status error">❌ 列表API返回错误：' + (result.msg || '未知错误') + '</div>';
                    responseDiv.textContent = JSON.stringify(result, null, 2);
                    purchaseDiv.innerHTML = '';
                }
            } catch (error) {
                statusDiv.innerHTML = '<div class="status error">❌ 网络错误：' + error.message + '</div>';
                responseDiv.textContent = '请求失败';
                purchaseDiv.innerHTML = '';
            }
        }

        // 页面加载时自动测试
        window.onload = function() {
            testPostDetail();
        };
    </script>
</body>
</html>
