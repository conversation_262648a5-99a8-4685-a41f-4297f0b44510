<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$site.name}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 375px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        /* 顶部图片区域 */
        .hero-section {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding-top: 0;
        }

        /* 标签区域 */
        .tabs-section {
            width: 100%;
            padding: 5px 0;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
        }

        .tabs-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 5px;
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 2px 8px;
            margin: 0 2px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(153, 153, 153, 0.8);
            color: white;
            border: none;
            outline: none;
            line-height: 1.2;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* 未点击状态 - 灰色毛玻璃 */
        .tab-item:not(.active) {
            background: rgba(153, 153, 153, 0.8);
            color: white;
        }

        /* 点击状态颜色 - 毛玻璃效果 */
        .tab-item.active.tab-1 {
            background: rgba(40, 167, 69, 0.8); /* 绿色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-2 {
            background: rgba(0, 123, 255, 0.8); /* 蓝色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-3 {
            background: rgba(220, 53, 69, 0.8); /* 红色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-4 {
            background: rgba(52, 58, 64, 0.8); /* 黑色毛玻璃 */
            color: white;
        }

        /* 悬停效果 */
        .tab-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }

        /* 开奖信息区域 */
        .lottery-info {
            width: 100%;
            padding: 5px 15px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .lottery-text {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        /* 开奖记录按钮 */
        .record-btn {
            padding: 4px 12px;
            border: none;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #8B5CF6 0%, #FFFFFF 100%);
            color: #333;
            outline: none;
            box-shadow: 0 2px 6px rgba(139, 92, 246, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        .record-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 10px rgba(139, 92, 246, 0.4);
        }

        /* 号码球区域 */
        .balls-section {
            width: 100%;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .balls-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: nowrap;
        }

        .ball-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            min-width: 40px;
            flex-shrink: 0;
        }

        .ball-wrapper {
            position: relative;
            width: 30px;
            height: 30px;
            margin-bottom: 5px;
        }

        .ball-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .ball-number {
            position: absolute;
            top: calc(50% - 2px);
            left: 50%;
            transform: translate(-50%, -50%);
            color: black;
            font-size: 16px;
            font-weight: bold;
            line-height: 1;
        }

        .ball-info {
            text-align: center;
            font-size: 10px;
            color: #666;
            font-weight: 600;
            line-height: 1.2;
        }

        /* 加号分隔符 */
        .plus-separator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #28a745;
            font-size: 20px;
            font-weight: bold;
            margin: 0 2px;
            flex-shrink: 0;
        }

        /* 开奖时间区域 */
        .draw-time-section {
            width: 100%;
            padding: 8px 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .draw-time-text {
            font-size: 11px;
            color: #666;
            font-weight: 500;
        }

        .draw-time-text .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        /* 刷新按钮 */
        .refresh-btn {
            padding: 3px 10px;
            border: none;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
            outline: none;
            box-shadow: 0 1px 4px rgba(255, 193, 7, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        .refresh-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
        }

        /* 红色区域 */
        .red-section {
            width: 375px;
            min-height: 210px;
            background: #ed0000;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0px 10px 0px 10px;
        }

        .red-content {
            display: flex;
            align-items: center;
            color: white;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .yellow-notice {
            color: #ffff00;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            line-height: 1.4;
        }

        .yellow-notice .small-text {
            font-size: 16px;
        }

        .arrow-icon {
            width: auto;
            height: auto;
            margin: 0 8px;
        }

        .arrow-left {
            transform: scaleX(-1);
        }

        /* 导航区域 */
        .nav-section {
            width: 375px;
            padding: 0;
            background: white;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
        }

        .nav-column {
            border-right: 1px solid #dee2e6;
        }

        .nav-column:last-child {
            border-right: none;
        }

        .nav-item {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #333;
            padding: 8px 6px;
            border-bottom: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            background: #f8f9fa;
        }

        .nav-item:last-child {
            border-bottom: none;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 12px;
            font-weight: bold;
            flex-shrink: 0;
            overflow: hidden;
        }

        .nav-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* 第一列红色 */
        .nav-column:nth-child(1) .nav-text {
            color: #dc3545;
        }

        /* 第二列蓝色 */
        .nav-column:nth-child(2) .nav-text {
            color: #007bff;
        }

        /* 第三列绿色 */
        .nav-column:nth-child(3) .nav-text {
            color: #28a745;
        }

        .nav-text {
            font-size: 12px;
            font-weight: 600;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 绿色长条 */
        .green-bar {
            width: 375px;
            height: 20px;
            background: #039e6d;
            display: flex;
            align-items: center;
            padding: 0 8px;
        }

        .green-bar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .green-bar-left {
            display: flex;
            align-items: center;
        }

        .green-bar-right {
            display: flex;
            align-items: center;
        }

        .home-icon {
            width: auto;
            height: 12px;
            margin-right: 3px;
        }

        .green-bar-clickable {
            color: white;
            font-size: 9px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            margin-right: 3px;
        }

        .green-bar-clickable:hover {
            text-decoration: underline;
        }

        .green-bar-text {
            color: white;
            font-size: 9px;
            font-weight: 600;
            margin-right: 3px;
        }

        .post-icon {
            width: auto;
            height: 12px;
            cursor: pointer;
        }

        .post-icon:hover {
            opacity: 0.8;
        }

        .user-info {
            color: white;
            font-size: 9px;
            font-weight: 600;
        }

        .user-link {
            color: white;
            font-size: 9px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            margin: 0 2px;
        }

        .user-link:hover {
            text-decoration: underline;
        }

        /* 登录表单 */
        .login-form {
            width: 375px;
            padding: 4px 8px;
            background: white;
            border-radius: 3px;
            margin-top: 1px;
        }

        .form-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 4px;
            flex-wrap: wrap;
        }

        .form-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .form-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .form-label {
            font-size: 9px;
            font-weight: 600;
            color: #333;
            white-space: nowrap;
        }

        .form-input {
            padding: 2px 4px;
            border: 1px solid #ccc;
            border-radius: 2px;
            font-size: 9px;
            width: 50px;
            outline: none;
        }

        .form-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 1px rgba(0, 123, 255, 0.3);
        }

        .form-btn {
            padding: 2px 6px;
            border: none;
            border-radius: 2px;
            font-size: 9px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
        }

        .form-btn:hover {
            background: linear-gradient(135deg, #e0a800 0%, #f8f9fa 100%);
            transform: translateY(-1px);
        }

        .search-input {
            padding: 2px 4px;
            border: 1px solid #ccc;
            border-radius: 2px;
            font-size: 9px;
            width: 60px;
            outline: none;
        }

        .search-input:focus {
            border-color: #28a745;
            box-shadow: 0 0 1px rgba(40, 167, 69, 0.3);
        }

        .search-btn {
            padding: 2px 6px;
            border: none;
            border-radius: 2px;
            font-size: 9px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
        }

        .search-btn:hover {
            background: linear-gradient(135deg, #e0a800 0%, #f8f9fa 100%);
            transform: translateY(-1px);
        }

        /* 滚动公告 */
        .notice-scroll {
            width: 375px;
            height: auto;
            background: white;
            overflow: hidden;
            display: flex;
            align-items: center;
            margin-top: 0;
            border-left: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
            border-radius: 0 0 3px 3px;
            padding: 8px 0;
        }

        .notice-content {
            color: #b91c1c;
            font-size: 16px;
            font-weight: 900;
            white-space: nowrap;
            animation: scroll-left 30s linear infinite;
            padding-left: 100%;
        }

        @keyframes scroll-left {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-100%);
            }
        }

        /* 右下角浮动菜单 */
        .floating-menu {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .menu-item {
            width: 45px;
            height: 45px;
            background: #000000cc;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 10px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 2px 7px rgba(0, 0, 0, 0.3);
        }

        /* 内容容器 */
        .content-container {
            width: 375px;
            min-height: 200px;
            background: white;
            border: 1px solid #28a745;
            margin-top: 0;
            padding: 10px;
        }

        /* 公告标题 */
        .announcement-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .announcement-icon {
            width: auto;
            height: 16px;
            margin-right: 6px;
        }

        .announcement-text {
            background: #ADEAEA;
            color: rgb(75, 0, 130);
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 4px;
            flex: 1;
        }

        /* 弹窗遮罩 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        /* 弹窗容器 */
        .modal {
            width: 90%;
            max-width: 400px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            transform: scale(0.7);
            transition: transform 0.3s ease;
        }

        .modal.show {
            transform: scale(1);
        }

        .modal-close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .modal-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .form-group label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            outline: none;
        }

        .form-group input:focus {
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
        }

        .modal-btn {
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            margin-top: 10px;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .error-message {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
        }

        .success-message {
            color: #28a745;
            font-size: 12px;
            margin-top: 5px;
        }

        /* 充币弹窗保持原样 */
        .recharge-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: flex-end;
            z-index: 2000;
        }

        .recharge-modal {
            width: 100%;
            background: white;
            border-radius: 12px 12px 0 0;
            padding: 20px;
            text-align: center;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .recharge-modal.show {
            transform: translateY(0);
        }

        .close-recharge {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }

        .recharge-qr {
            width: 200px;
            height: auto;
            margin: 10px 0 15px 0;
        }

        .recharge-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-top: 10px;
        }









        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }

            .mobile-container {
                width: 100%;
                box-shadow: none;
            }

            .red-section {
                width: 100%;
            }

            .nav-section {
                width: 100%;
            }

            .green-bar {
                width: 100%;
            }

            .login-form {
                width: 100%;
            }

            .notice-scroll {
                width: 100%;
            }

            .content-container {
                width: 100%;
            }

            /* 帖子列表样式 - 与公告格式一致 */
            .post-header {
                display: flex;
                align-items: center;
                padding: 8px 0px;
                margin-bottom: 5px;
            }

            .post-icon {
                width: 48px;
                height: 16px;
                margin-right: 8px;
                flex-shrink: 0;
                object-fit: cover;
            }

            .post-text {
                flex: 1;
                font-size: 14px;
                line-height: 1.4;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            /* 根据分类ID设置不同颜色 */
            .post-text.category-7 {
                color: red;
            }

            .post-text.category-8 {
                color: #ff0d86;
            }

            .post-text.category-9 {
                color: blue;
            }

        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 顶部图片区域 -->
        <section class="hero-section">
            <img border="0" src="{$site.top_images}" width="100%" height="180">
        </section>

        <!-- 标签区域 -->
        <section class="tabs-section">
            <div class="tabs-container">
                <button class="tab-item tab-1 active" onclick="selectTab(1)">私彩1</button>
                <button class="tab-item tab-2" onclick="selectTab(2)">新澳</button>
                <button class="tab-item tab-3" onclick="selectTab(3)">香港</button>
                <button class="tab-item tab-4" onclick="selectTab(4)">老澳</button>
            </div>
        </section>

        <!-- 开奖信息区域 -->
        <section class="lottery-info">
            <div class="lottery-text" id="lotteryText">
                私彩 第<span class="period-number">200</span>期开奖结果:
            </div>
            <button class="record-btn" onclick="showRecord()">开奖记录</button>
        </section>

        <!-- 号码球区域 -->
        <section class="balls-section">
            <div class="balls-container" id="ballsContainer">
                <!-- 7个号码球将通过JavaScript动态生成 -->
            </div>
        </section>

        <!-- 开奖时间区域 -->
        <section class="draw-time-section">
            <div class="draw-time-text" id="drawTimeText">
                第<span class="period-number">200</span>期开奖时间07月28日 周一21点32分
            </div>
            <button class="refresh-btn" onclick="refreshData()">刷新</button>
        </section>
    </div>

    <!-- 红色区域 -->
    <div class="red-section">
        <div class="red-content">
            <!--<img src="/images/shuangjiantou.gif" alt="箭头" class="arrow-icon arrow-left">
            <span>六合宝典◆发帖有奖</span>
            <img src="/images/shuangjiantou.gif" alt="箭头" class="arrow-icon">-->
        </div>
        <div class="yellow-notice">
            {$site.noito_msg}
        </div>
    </div>

    <!-- 导航区域 -->
    <div class="nav-section">
        <div class="nav-grid" id="navigationGrid">
            <!-- 导航内容将通过JavaScript动态加载 -->
            <div class="nav-loading" style="text-align: center; padding: 20px; color: #666;">
                正在加载导航...
            </div>
        </div>
    </div>

    <!-- 绿色长条 -->
    <div class="green-bar">
        <div class="green-bar-content">
            <div class="green-bar-left">
                <img src="/images/home.gif" alt="首页" class="home-icon">
                <a href="#" class="green-bar-clickable" onclick="contactAdmin()">『澳门内部最准资料』</a>
                <span class="green-bar-text">☎ 论坛管理員：</span>
                <img src="/images/post.gif" alt="发帖" class="post-icon" onclick="postMessage()">
            </div>
            <div class="green-bar-right" id="userStatusArea">
                <span class="user-info" id="userStatusText">您当前是游客：</span>
                <a href="#" class="user-link" id="loginLink" onclick="showLogin()">登录</a>
                <span class="user-info" id="divider">|</span>
                <a href="#" class="user-link" id="registerLink" onclick="showRegister()">注册</a>
            </div>
        </div>
    </div>

    <!-- 登录表单 -->
    <div class="login-form" id="loginFormArea">
        <div class="form-row">
            <div class="form-left">
                <label class="form-label">用户名:</label>
                <input type="text" id="homeLoginAccount" class="form-input" placeholder="用户名">
                <label class="form-label">密码:</label>
                <input type="password" id="homeLoginPassword" class="form-input" placeholder="密码">
                <button class="form-btn btn-login" onclick="handleHomeLogin()">登录</button>
                <button class="form-btn btn-register" onclick="handleRegister()">注册</button>
            </div>
            <div class="form-right">
                <input type="text" class="search-input" placeholder="搜索">
                <button class="search-btn" onclick="handleSearch()">搜索</button>
            </div>
        </div>
    </div>

    <!-- 滚动公告 -->
    <div class="notice-scroll">
        <div class="notice-content">
            {$site.msg}
        </div>
    </div>

    <!-- 内容容器 -->
    <div class="content-container">
        <!-- 公告标题 -->
        <div class="announcement-header">
            <img src="{$site.tu1}" alt="公告图标" class="announcement-icon">
            <div class="announcement-text">
                {$site.notio1}
            </div>
        </div>

        <!-- 第二条公告 -->
        <div class="announcement-header">
            <img src="{$site.tu1}" alt="公告图标" class="announcement-icon">
            <div class="announcement-text">
                {$site.notio2}
            </div>
        </div>
        <!-- 帖子列表区域 -->
        <div id="postsList">
            <!-- 帖子列表将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 充币弹窗遮罩 -->
    <div class="recharge-overlay" id="rechargeOverlay">
        <div class="recharge-modal">
            <span class="close-recharge" onclick="closeRecharge()">&times;</span>
            <img src="{$site.kefu}" alt="充币二维码" class="recharge-qr">
            <div class="recharge-title">扫码联系客服</div>
        </div>
    </div>

    <!-- 右下角浮动菜单 -->
    <div class="floating-menu">
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('注册')">注册</a>
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('充币')">充币</a>
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('优惠')">优惠</a>
        <a href="javascript:void(0)" class="menu-item" onclick="handleMenuClick('介绍')">介绍</a>
    </div>

    <script>
        function selectTab(tabNumber) {
            // 移除所有标签的active类
            const allTabs = document.querySelectorAll('.tab-item');
            allTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 给点击的标签添加active类
            const selectedTab = document.querySelector(`.tab-${tabNumber}`);
            selectedTab.classList.add('active');

            // 更新开奖信息文本
            const lotteryText = document.getElementById('lotteryText');
            const lotteryNames = {
                1: '私彩',
                2: '新澳',
                3: '香港',
                4: '老澳'
            };

            const periodNumbers = {
                1: '200',
                2: '156',
                3: '089',
                4: '234'
            };

            lotteryText.innerHTML = `${lotteryNames[tabNumber]} 第<span class="period-number">${periodNumbers[tabNumber]}</span>期开奖结果:`;

            // 更新号码球
            updateBalls(tabNumber);

            // 更新开奖时间
            updateDrawTime(tabNumber);
        }

        function updateBalls(tabNumber) {
            const ballsContainer = document.getElementById('ballsContainer');
            const ballsData = {
                1: [
                    { number: '08', zodiac: '鼠', element: '水', color: 'blue' },
                    { number: '15', zodiac: '兔', element: '木', color: 'green' },
                    { number: '23', zodiac: '猪', element: '火', color: 'red' },
                    { number: '31', zodiac: '马', element: '火', color: 'red' },
                    { number: '42', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '07', zodiac: '牛', element: '土', color: 'green' },
                    { number: '19', zodiac: '羊', element: '土', color: 'red' }
                ],
                2: [
                    { number: '03', zodiac: '兔', element: '木', color: 'green' },
                    { number: '12', zodiac: '鼠', element: '水', color: 'blue' },
                    { number: '25', zodiac: '龙', element: '土', color: 'red' },
                    { number: '34', zodiac: '虎', element: '木', color: 'green' },
                    { number: '41', zodiac: '马', element: '火', color: 'red' },
                    { number: '06', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '18', zodiac: '鸡', element: '金', color: 'blue' }
                ],
                3: [
                    { number: '01', zodiac: '猪', element: '火', color: 'red' },
                    { number: '14', zodiac: '虎', element: '木', color: 'green' },
                    { number: '27', zodiac: '兔', element: '木', color: 'green' },
                    { number: '33', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '45', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '09', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '22', zodiac: '虎', element: '木', color: 'green' }
                ],
                4: [
                    { number: '05', zodiac: '马', element: '火', color: 'red' },
                    { number: '16', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '28', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '37', zodiac: '猪', element: '火', color: 'red' },
                    { number: '44', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '11', zodiac: '猪', element: '火', color: 'red' },
                    { number: '29', zodiac: '马', element: '火', color: 'red' }
                ]
            };

            const balls = ballsData[tabNumber];
            ballsContainer.innerHTML = '';

            balls.forEach((ball, index) => {
                const ballItem = document.createElement('div');
                ballItem.className = 'ball-item';
                ballItem.innerHTML = `
                    <div class="ball-wrapper">
                        <img src="/images/ball-${ball.color}.png" alt="${ball.color} ball" class="ball-image">
                        <div class="ball-number">${ball.number}</div>
                    </div>
                    <div class="ball-info">${ball.zodiac}/${ball.element}</div>
                `;
                ballsContainer.appendChild(ballItem);

                // 在第6个球后添加加号分隔符
                if (index === 5) {
                    const plusSeparator = document.createElement('div');
                    plusSeparator.className = 'plus-separator';
                    plusSeparator.innerHTML = '+';
                    ballsContainer.appendChild(plusSeparator);
                }
            });
        }

        function updateDrawTime(tabNumber) {
            const drawTimeText = document.getElementById('drawTimeText');
            const drawTimes = {
                1: { period: '200', time: '开奖时间07月28日 周一21点32分' },
                2: { period: '156', time: '开奖时间07月28日 周一20点15分' },
                3: { period: '089', time: '开奖时间07月28日 周一19点45分' },
                4: { period: '234', time: '开奖时间07月28日 周一22点10分' }
            };

            const drawTime = drawTimes[tabNumber];
            drawTimeText.innerHTML = `第<span class="period-number">${drawTime.period}</span>期${drawTime.time}`;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateBalls(1); // 默认显示私彩的球
            updateDrawTime(1); // 默认显示私彩的开奖时间
            loadNavigation(); // 加载导航数据
            checkUserLoginStatus(); // 检查用户登录状态
        });

        // 检查用户登录状态
        function checkUserLoginStatus() {
            const userToken = localStorage.getItem('userToken');
            const userInfo = localStorage.getItem('userInfo');

            if (userToken && userInfo) {
                try {
                    const user = JSON.parse(userInfo);
                    // 更新状态显示
                    document.getElementById('userStatusText').textContent = `欢迎，${user.username || user.nickname || '用户'}：`;

                    // 更新链接
                    const statusArea = document.getElementById('userStatusArea');
                    statusArea.innerHTML = `
                        <span class="user-info" id="userStatusText">欢迎，${user.username || user.nickname || '用户'}：</span>
                        <a href="/index/index/profile" class="user-link">控制中心</a>
                        <span class="user-info">|</span>
                        <a href="#" class="user-link" onclick="logout()">退出</a>
                    `;

                    // 隐藏登录表单
                    document.getElementById('loginFormArea').style.display = 'none';
                } catch (error) {
                    console.error('用户信息解析失败:', error);
                    localStorage.removeItem('userToken');
                    localStorage.removeItem('userInfo');
                }
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('userToken');
            localStorage.removeItem('userInfo');
            location.reload(); // 刷新页面
        }

        // 首页登录处理
        async function handleHomeLogin() {
            const account = document.getElementById('homeLoginAccount').value.trim();
            const password = document.getElementById('homeLoginPassword').value.trim();

            if (!account || !password) {
                alert('请输入用户名和密码');
                return;
            }

            try {
                const response = await fetch('/api/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        account: account,
                        password: password
                    })
                });

                const result = await response.json();

                if (result.code === 1) {
                    // 登录成功
                    if (result.data && result.data.userinfo) {
                        localStorage.setItem('userToken', result.data.userinfo.token);
                        localStorage.setItem('userInfo', JSON.stringify(result.data.userinfo));
                    }

                    alert('登录成功！');
                    checkUserLoginStatus(); // 更新登录状态显示
                } else {
                    alert(result.msg || '登录失败，请检查用户名和密码');
                }
            } catch (error) {
                console.error('登录请求失败:', error);
                alert('网络错误，请稍后重试');
            }
        }

        // 加载导航数据
        async function loadNavigation() {
            try {
                const response = await fetch('/api/navigation/index');
                const result = await response.json();

                if (result.code === 1 && result.data && result.data.length > 0) {
                    renderNavigation(result.data);
                } else {
                    showNavigationError('暂无导航数据');
                }
            } catch (error) {
                console.error('加载导航失败:', error);
                showNavigationError('导航加载失败');
            }
        }

        // 渲染导航
        function renderNavigation(navigationData) {
            const navigationGrid = document.getElementById('navigationGrid');

            // 将导航数据按3列分组
            const columns = [[], [], []];
            navigationData.forEach((nav, index) => {
                columns[index % 3].push(nav);
            });

            // 生成HTML
            let html = '';
            columns.forEach((column, columnIndex) => {
                html += '<div class="nav-column">';
                column.forEach(nav => {
                    const iconHtml = nav.image ?
                        `<img src="${nav.image}" alt="${nav.title}">` :
                        `<span>${nav.title.charAt(0)}</span>`;

                    html += `
                        <a href="${nav.url}" class="nav-item" target="${nav.target}" onclick="navigateTo('${nav.title}')">
                            <div class="nav-icon">${iconHtml}</div>
                            <div class="nav-text">${nav.title}</div>
                        </a>
                    `;
                });
                html += '</div>';
            });

            navigationGrid.innerHTML = html;
        }

        // 显示导航错误信息
        function showNavigationError(message) {
            const navigationGrid = document.getElementById('navigationGrid');
            navigationGrid.innerHTML = `
                <div class="nav-loading" style="text-align: center; padding: 20px; color: #dc3545;">
                    ${message}
                </div>
            `;
        }

        function refreshData() {
            // 获取当前选中的标签
            const activeTab = document.querySelector('.tab-item.active');
            const tabNumber = activeTab.classList.contains('tab-1') ? 1 :
                            activeTab.classList.contains('tab-2') ? 2 :
                            activeTab.classList.contains('tab-3') ? 3 : 4;

            // 重新加载数据
            updateBalls(tabNumber);
            updateDrawTime(tabNumber);

            // 简单的刷新提示
            alert('数据已刷新');
        }

        function showRecord() {
            alert('开奖记录功能');
        }

        function navigateTo(forumName) {
            // 这个函数现在主要用于统计点击，实际跳转由链接的href处理
            console.log('导航点击：' + forumName);
            // 可以在这里添加统计代码
        }

        function contactAdmin() {
            alert('澳门内部最准资料');
        }

        function postMessage() {
            alert('发帖功能');
        }

        function showLogin() {
            window.location.href = '/index/index/login';
        }

        function showRegister() {
            // 检查是否已登录
            const userToken = localStorage.getItem('userToken');
            if (userToken) {
                alert('您已登录，无需重复登录！');
                return;
            }
            window.location.href = '/index/index/register';
        }

        function handleLogin() {
            window.location.href = '/index/index/login';
        }

        function handleRegister() {
            // 检查是否已登录
            const userToken = localStorage.getItem('userToken');
            if (userToken) {
                alert('您已登录，无需重复登录！');
                return;
            }
            window.location.href = '/index/index/register';
        }

        function showProfile() {
            window.location.href = '/index/index/profile';
        }

        function handleSearch() {
            const searchText = document.querySelector('.search-input').value;
            if (searchText) {
                alert('搜索：' + searchText);
            } else {
                alert('请输入搜索内容');
            }
        }

        function handleMenuClick(action) {
            if (action === '注册') {
                // 检查是否已登录
                const userToken = localStorage.getItem('userToken');
                if (userToken) {
                    alert('您已登录，无需重复登录！');
                    return;
                }
                window.location.href = '/index/index/register';
            } else if (action === '充币') {
                openRecharge();
            } else if (action === '优惠') {
                window.location.href = '/index/index/promotion';
            } else if (action === '介绍') {
                window.location.href = '/index/index/about';
            } else {
                alert(action + '功能');
            }
        }

        function openRecharge() {
            const overlay = document.getElementById('rechargeOverlay');
            const modal = overlay.querySelector('.recharge-modal');
            overlay.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);

            // 防止页面滚动
            document.body.style.overflow = 'hidden';
        }

        function closeRecharge() {
            const overlay = document.getElementById('rechargeOverlay');
            const modal = overlay.querySelector('.recharge-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                overlay.style.display = 'none';
                // 恢复页面滚动
                document.body.style.overflow = '';
            }, 300);
        }

        // 点击空白区域关闭弹窗
        // 加载帖子列表
        function loadPosts() {
            const postsList = document.getElementById('postsList');

            fetch('/api/posts?limit=10')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1 && data.data && data.data.list) {
                        renderPosts(data.data.list);
                    } else {
                        // 如果没有帖子数据，不显示任何内容
                        postsList.innerHTML = '';
                    }
                })
                .catch(error => {
                    console.error('加载帖子失败:', error);
                    // 加载失败也不显示错误信息，保持空白
                    postsList.innerHTML = '';
                });
        }

        // 渲染帖子列表
        function renderPosts(posts) {
            const postsList = document.getElementById('postsList');

            if (posts.length === 0) {
                postsList.innerHTML = '';
                return;
            }

            let html = '';
            posts.forEach(post => {
                const iconUrl = post.icon_url;
                const categoryId = post.category.id;
                const categoryClass = `category-${categoryId}`;

                // 检查是否有收费内容
                const hasPaidContent = post.paid_content_index > 0 && post.paid_content_price > 0;
                const paidBadge = hasPaidContent ? `<span style="background: #ffc107; color: #212529; padding: 2px 6px; border-radius: 10px; font-size: 10px; margin-left: 5px; font-weight: 500;">🔒${post.paid_content_price}金币</span>` : '';

                html += `
                    <div class="post-header" onclick="viewPost(${post.id})">
                        <img src="${iconUrl}" alt="帖子图标" class="post-icon">
                        <div class="post-text ${categoryClass}">
                            ${post.title}${paidBadge}
                        </div>
                    </div>
                `;
            });

            postsList.innerHTML = html;
        }

        // 查看帖子详情
        function viewPost(postId) {
            window.location.href = `/index/index/post_detail?id=${postId}`;
        }

        document.addEventListener('DOMContentLoaded', function() {
            const overlay = document.getElementById('rechargeOverlay');
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeRecharge();
                }
            });

            // 加载帖子列表
            loadPosts();
        });
    </script>
</body>
</html>
