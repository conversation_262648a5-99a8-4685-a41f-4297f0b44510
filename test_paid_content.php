<?php
/**
 * 收费内容功能测试脚本
 * 使用方法：在浏览器中访问此文件进行测试
 */

// 引入框架
require_once 'thinkphp/start.php';

use app\common\model\Post;
use app\common\model\PostCategory;
use app\common\model\User;

echo "<h1>收费内容功能测试</h1>";

// 1. 创建测试分类
echo "<h2>1. 创建测试分类</h2>";
$category = PostCategory::where('name', '测试分类')->find();
if (!$category) {
    $category = PostCategory::create([
        'name' => '测试分类',
        'description' => '用于测试收费内容功能',
        'status' => 1,
        'sort_order' => 0
    ]);
    echo "✅ 创建测试分类成功，ID: " . $category->id . "<br>";
} else {
    echo "✅ 测试分类已存在，ID: " . $category->id . "<br>";
}

// 2. 创建测试用户
echo "<h2>2. 创建测试用户</h2>";
$user = User::where('username', 'testuser')->find();
if (!$user) {
    $user = User::create([
        'username' => 'testuser',
        'nickname' => '测试用户',
        'password' => md5('123456'),
        'salt' => 'test',
        'email' => '<EMAIL>',
        'score' => 1000, // 给1000积分用于测试
        'status' => 'normal',
        'group_id' => 1
    ]);
    echo "✅ 创建测试用户成功，ID: " . $user->id . "，积分: " . $user->score . "<br>";
} else {
    echo "✅ 测试用户已存在，ID: " . $user->id . "，积分: " . $user->score . "<br>";
}

// 3. 创建测试帖子（包含收费内容）
echo "<h2>3. 创建测试帖子</h2>";
$testContent = "这是第一段免费内容，大家都可以看到。
[SPLIT]
这是第二段免费内容，介绍一些基础知识。
[SPLIT]
这是第三段收费内容，包含核心技术和高级配置方法，需要支付积分才能查看。
[SPLIT]
这是第四段免费内容，总结和后续建议。";

$post = Post::create([
    'user_id' => $user->id,
    'category_id' => $category->id,
    'title' => '测试收费内容帖子',
    'content' => $testContent,
    'post_type' => 'expert',
    'paid_content_index' => 3, // 第3段收费
    'paid_content_price' => 50, // 50积分
    'status' => 'published'
]);

echo "✅ 创建测试帖子成功，ID: " . $post->id . "<br>";
echo "📝 帖子标题: " . $post->title . "<br>";
echo "💰 收费段落: 第" . $post->paid_content_index . "段<br>";
echo "💎 收费价格: " . $post->paid_content_price . "积分<br>";

// 4. 测试内容显示（未购买）
echo "<h2>4. 测试内容显示（未购买）</h2>";
$displayContent = $post->getDisplayContent();
echo "<div style='border: 1px solid #ddd; padding: 15px; background: #f9f9f9;'>";
echo $displayContent;
echo "</div>";

// 5. 测试内容显示（已购买）
echo "<h2>5. 测试内容显示（已购买）</h2>";
// 模拟购买记录
\app\common\model\PostPurchase::create([
    'user_id' => $user->id,
    'post_id' => $post->id,
    'price' => $post->paid_content_price,
    'status' => 1
]);

$displayContentPurchased = $post->getDisplayContent($user->id);
echo "<div style='border: 1px solid #ddd; padding: 15px; background: #f0f8ff;'>";
echo $displayContentPurchased;
echo "</div>";

// 6. 测试API接口
echo "<h2>6. API接口测试链接</h2>";
echo "<p>📋 <a href='/api/posts/detail?id=" . $post->id . "' target='_blank'>查看帖子详情API</a></p>";
echo "<p>🛒 购买接口需要POST请求，可以使用前端页面测试</p>";

echo "<h2>✅ 测试完成</h2>";
echo "<p>现在可以：</p>";
echo "<ul>";
echo "<li>访问后台管理，编辑帖子ID " . $post->id . " 来测试后台编辑功能</li>";
echo "<li>访问前端帖子详情页面来测试用户购买流程</li>";
echo "<li>检查数据库中的购买记录和积分日志</li>";
echo "</ul>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #333; }
h2 { color: #666; border-bottom: 1px solid #eee; padding-bottom: 5px; }
</style>";
?>
