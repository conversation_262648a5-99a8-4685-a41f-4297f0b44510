<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddPaidContentFields extends Migrator
{
    /**
     * 添加收费内容相关字段
     */
    public function change()
    {
        $table = $this->table('posts');
        
        // 添加分段收费内容字段
        $table->addColumn('paid_content_index', 'integer', [
                'signed' => false, 
                'default' => 0, 
                'comment' => '收费内容段落索引(从1开始,0表示无收费内容)',
                'after' => 'vip_price'
            ])
            ->addColumn('paid_content_price', 'integer', [
                'signed' => false, 
                'default' => 0, 
                'comment' => '收费内容价格(积分)',
                'after' => 'paid_content_index'
            ])
            ->update();
    }
}
