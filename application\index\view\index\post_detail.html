<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$site.title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            justify-content: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 375px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #00d1ff;
            padding: 15px;
            text-align: center;
            position: relative;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
        }

        .back-btn {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            text-decoration: none;
        }

        .back-btn img {
            width: auto;
            height: 20px;
            vertical-align: middle;
        }

        /* 蓝色长条 */
        .blue-bar {
            width: 100%;
            height: 20px;
            background: #a9d5e9;
            display: flex;
            align-items: center;
            padding: 0 10px;
        }

        .blue-bar-theme {
            color: #0b78ff;
            font-size: 10px;
            font-weight: 600;
            margin-right: 6px;
            white-space: nowrap;
        }

        .blue-bar-title {
            color: black;
            font-size: 10px;
            font-weight: 500;
            flex: 1;
        }

        .blue-bar-right {
            display: flex;
            align-items: center;
            font-size: 10px;
            white-space: nowrap;
        }

        .blue-bar-views {
            color: black;
            margin-right: 8px;
        }

        .view-count {
            color: red;
        }

        .blue-bar-close {
            color: black;
            text-decoration: none;
        }

        .blue-bar-close:hover {
            text-decoration: underline;
        }

        /* 标签区域 */
        .tabs-section {
            width: 100%;
            padding: 5px 0;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
        }

        .tabs-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 5px;
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 2px 8px;
            margin: 0 2px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(153, 153, 153, 0.8);
            color: white;
            border: none;
            outline: none;
            line-height: 1.2;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .tab-item.active.tab-1 {
            background: rgba(40, 167, 69, 0.8);
            color: white;
        }

        .tab-item.active.tab-2 {
            background: rgba(0, 123, 255, 0.8);
            color: white;
        }

        .tab-item.active.tab-3 {
            background: rgba(220, 53, 69, 0.8);
            color: white;
        }

        .tab-item.active.tab-4 {
            background: rgba(52, 58, 64, 0.8);
            color: white;
        }

        /* 开奖信息区域 */
        .lottery-info {
            width: 100%;
            padding: 5px 15px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .lottery-text {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        .record-btn {
            padding: 4px 12px;
            border: none;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #8B5CF6 0%, #FFFFFF 100%);
            color: #333;
            outline: none;
            box-shadow: 0 2px 6px rgba(139, 92, 246, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        /* 号码球区域 */
        .balls-section {
            width: 100%;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .balls-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: nowrap;
        }

        .ball-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            min-width: 40px;
            flex-shrink: 0;
        }

        .ball-wrapper {
            position: relative;
            width: 30px;
            height: 30px;
            margin-bottom: 5px;
        }

        .ball-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .ball-number {
            position: absolute;
            top: calc(50% - 2px);
            left: 50%;
            transform: translate(-50%, -50%);
            color: black;
            font-size: 16px;
            font-weight: bold;
            line-height: 1;
        }

        .ball-info {
            text-align: center;
            font-size: 10px;
            color: #666;
            font-weight: 600;
            line-height: 1.2;
        }

        .plus-separator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #28a745;
            font-size: 20px;
            font-weight: bold;
            margin: 0 2px;
            flex-shrink: 0;
        }

        /* 开奖时间区域 */
        .draw-time-section {
            width: 100%;
            padding: 8px 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .draw-time-text {
            font-size: 11px;
            color: #666;
            font-weight: 500;
        }

        .refresh-btn {
            padding: 3px 10px;
            border: none;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
            outline: none;
            box-shadow: 0 1px 4px rgba(255, 193, 7, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        /* 内容区域 */
        .content-area {
            min-height: calc(100vh - 60px);
            padding: 15px;
        }

        /* 帖子内容 */
        .post-content {
            line-height: 1.6;
            color: #333;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 10px 0;
        }

        .post-content p {
            margin-bottom: 10px;
        }

        /* 帖子统计 */
        .post-stats {
            display: flex;
            justify-content: space-around;
            padding: 15px 0;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            color: #999;
            margin-top: 2px;
        }

        /* 作者信息 */
        .author-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .author-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .author-details {
            flex: 1;
        }

        .author-name {
            font-size: 14px;
            color: #333;
            font-weight: 600;
        }

        .post-time {
            font-size: 12px;
            color: #999;
            margin-top: 2px;
        }

        .post-type {
            background: #00d1ff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            margin-left: auto;
        }

        .post-type.sale {
            background: #ff6b6b;
        }

        .post-type.hot {
            background: #ff9500;
        }

        .post-type.expert {
            background: #9c27b0;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }

        .error {
            text-align: center;
            padding: 40px 20px;
            color: #ff6b6b;
        }

        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }

            .mobile-container {
                width: 100%;
                box-shadow: none;
            }
        }

        /* 帖子内容 */
        .post-content {
            line-height: 1.6;
            color: #333;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 10px 0;
        }

        .post-content p {
            margin-bottom: 10px;
        }

        /* 帖子统计 */
        .post-stats {
            display: flex;
            justify-content: space-around;
            padding: 15px 0;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            color: #999;
            margin-top: 2px;
        }

        /* 底部操作栏 */
        .bottom-actions {
            margin-top: 20px;
            padding: 15px 0;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-size: 12px;
        }

        .action-btn:hover {
            color: #00d1ff;
        }

        .action-btn.active {
            color: #00d1ff;
        }

        .action-icon {
            font-size: 18px;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }

        .error {
            text-align: center;
            padding: 40px 20px;
            color: #ff6b6b;
        }

        /* 响应式调整 */
        @media (max-width: 375px) {
            .mobile-container, .top-nav, .bottom-actions {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <a href="/" class="back-btn">
                <img src="/images/balk.png" alt="返回">
            </a>
            <div class="page-title">帖子详情</div>
        </div>

        <!-- 标签区域 -->
        <section class="tabs-section">
            <div class="tabs-container">
                <div class="tab-item tab-1 active" onclick="selectTab(1)">私彩1</div>
                <div class="tab-item tab-2" onclick="selectTab(2)">新澳</div>
                <div class="tab-item tab-3" onclick="selectTab(3)">香港</div>
                <div class="tab-item tab-4" onclick="selectTab(4)">老澳</div>
            </div>
        </section>

        <!-- 开奖信息区域 -->
        <section class="lottery-info">
            <div class="lottery-text" id="lotteryText">
                私彩 第<span class="period-number">200</span>期开奖结果:
            </div>
            <button class="record-btn" onclick="showRecord()">开奖记录</button>
        </section>

        <!-- 号码球区域 -->
        <section class="balls-section">
            <div class="balls-container" id="ballsContainer">
                <!-- 7个号码球将通过JavaScript动态生成 -->
            </div>
        </section>

        <!-- 开奖时间区域 -->
        <section class="draw-time-section">
            <div class="draw-time-text" id="drawTimeText">
                第<span class="period-number">200</span>期开奖时间07月28日 周一21点32分
            </div>
            <button class="refresh-btn" onclick="refreshData()">刷新</button>
        </section>

        <!-- 蓝色长条 -->
        <div class="blue-bar">
            <span class="blue-bar-theme">帖子:</span>
            <span class="blue-bar-title" id="postTitle">加载中...</span>
            <div class="blue-bar-right">
                <span class="blue-bar-views">阅读<span class="view-count" id="viewCount">12345</span>次</span>
                <span class="blue-bar-separator"> | </span>
                <a href="javascript:void(0)" class="blue-bar-close" onclick="closePage()">关闭本页</a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area" id="contentArea">
            <div class="loading">加载中...</div>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 加载帖子详情
        function loadPostDetail() {
            const postId = getUrlParam('id');
            if (!postId) {
                showError('参数错误');
                return;
            }

            fetch(`/api/posts/detail?id=${postId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1 && data.data) {
                        renderPostDetail(data.data);
                    } else {
                        showError(data.msg || '帖子不存在');
                    }
                })
                .catch(error => {
                    console.error('加载帖子详情失败:', error);
                    showError('加载失败，请稍后重试');
                });
        }

        // 渲染帖子详情
        function renderPostDetail(post) {
            const contentArea = document.getElementById('contentArea');
            const postTitle = document.getElementById('postTitle');
            const viewCount = document.getElementById('viewCount');

            const postTypeClass = post.post_type || 'normal';
            const postTypeName = getPostTypeName(post.post_type);
            const createTime = formatTime(post.createtime);

            // 生成万起的随机阅读数
            const randomViews = Math.floor(Math.random() * 90000) + 10000; // 10000-99999

            // 更新蓝色长条的标题和阅读数
            postTitle.textContent = post.title;
            viewCount.textContent = randomViews;

            const html = `
                <div class="post-content">
                    ${post.content}
                </div>
            `;

            contentArea.innerHTML = html;

            // 更新页面标题
            document.title = post.title;
        }

        // 显示错误信息
        function showError(message) {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = `<div class="error">${message}</div>`;
        }

        // 获取帖子类型名称
        function getPostTypeName(type) {
            const types = {
                'normal': '普通帖',
                'sale': '出售帖',
                'hot': '热门帖',
                'expert': '高手帖'
            };
            return types[type] || '普通帖';
        }

        // 格式化时间
        function formatTime(timestamp) {
            const date = new Date(timestamp * 1000);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) {
                return '刚刚';
            } else if (diff < 3600000) {
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) {
                return Math.floor(diff / 3600000) + '小时前';
            } else {
                return date.toLocaleDateString();
            }
        }

        // 关闭页面
        function closePage() {
            if (document.referrer) {
                history.back();
            } else {
                window.location.href = '/';
            }
        }

        // 选择标签
        function selectTab(tabNumber) {
            const allTabs = document.querySelectorAll('.tab-item');
            allTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            const selectedTab = document.querySelector(`.tab-${tabNumber}`);
            selectedTab.classList.add('active');

            const lotteryText = document.getElementById('lotteryText');
            const lotteryNames = {
                1: '私彩',
                2: '新澳',
                3: '香港',
                4: '老澳'
            };

            const periodNumbers = {
                1: '200',
                2: '156',
                3: '089',
                4: '234'
            };

            lotteryText.innerHTML = `${lotteryNames[tabNumber]} 第<span class="period-number">${periodNumbers[tabNumber]}</span>期开奖结果:`;
            updateBalls(tabNumber);
            updateDrawTime(tabNumber);
        }

        // 更新号码球
        function updateBalls(tabNumber) {
            const ballsContainer = document.getElementById('ballsContainer');
            const ballsData = {
                1: [
                    { number: '08', zodiac: '鼠', element: '水', color: 'blue' },
                    { number: '15', zodiac: '兔', element: '木', color: 'green' },
                    { number: '23', zodiac: '猪', element: '火', color: 'red' },
                    { number: '31', zodiac: '马', element: '火', color: 'red' },
                    { number: '42', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '07', zodiac: '牛', element: '土', color: 'green' },
                    { number: '19', zodiac: '羊', element: '土', color: 'red' }
                ],
                2: [
                    { number: '03', zodiac: '兔', element: '木', color: 'green' },
                    { number: '12', zodiac: '鼠', element: '水', color: 'blue' },
                    { number: '25', zodiac: '龙', element: '土', color: 'red' },
                    { number: '34', zodiac: '虎', element: '木', color: 'green' },
                    { number: '41', zodiac: '马', element: '火', color: 'red' },
                    { number: '06', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '18', zodiac: '鸡', element: '金', color: 'blue' }
                ],
                3: [
                    { number: '01', zodiac: '猪', element: '火', color: 'red' },
                    { number: '14', zodiac: '虎', element: '木', color: 'green' },
                    { number: '27', zodiac: '兔', element: '木', color: 'green' },
                    { number: '33', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '45', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '09', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '22', zodiac: '虎', element: '木', color: 'green' }
                ],
                4: [
                    { number: '05', zodiac: '马', element: '火', color: 'red' },
                    { number: '16', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '28', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '37', zodiac: '猪', element: '火', color: 'red' },
                    { number: '44', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '11', zodiac: '猪', element: '火', color: 'red' },
                    { number: '29', zodiac: '马', element: '火', color: 'red' }
                ]
            };

            const balls = ballsData[tabNumber];
            ballsContainer.innerHTML = '';

            balls.forEach((ball, index) => {
                const ballItem = document.createElement('div');
                ballItem.className = 'ball-item';
                ballItem.innerHTML = `
                    <div class="ball-wrapper">
                        <img src="/images/ball-${ball.color}.png" alt="${ball.color} ball" class="ball-image">
                        <div class="ball-number">${ball.number}</div>
                    </div>
                    <div class="ball-info">${ball.zodiac}/${ball.element}</div>
                `;
                ballsContainer.appendChild(ballItem);

                if (index === 5) {
                    const plusSeparator = document.createElement('div');
                    plusSeparator.className = 'plus-separator';
                    plusSeparator.innerHTML = '+';
                    ballsContainer.appendChild(plusSeparator);
                }
            });
        }

        // 更新开奖时间
        function updateDrawTime(tabNumber) {
            const drawTimeText = document.getElementById('drawTimeText');
            const drawTimes = {
                1: { period: '200', time: '开奖时间07月28日 周一21点32分' },
                2: { period: '156', time: '开奖时间07月28日 周一20点15分' },
                3: { period: '089', time: '开奖时间07月28日 周一19点45分' },
                4: { period: '234', time: '开奖时间07月28日 周一22点10分' }
            };

            const drawTime = drawTimes[tabNumber];
            drawTimeText.innerHTML = `第<span class="period-number">${drawTime.period}</span>期${drawTime.time}`;
        }

        // 刷新数据
        function refreshData() {
            const activeTab = document.querySelector('.tab-item.active');
            const tabNumber = activeTab.classList.contains('tab-1') ? 1 :
                            activeTab.classList.contains('tab-2') ? 2 :
                            activeTab.classList.contains('tab-3') ? 3 : 4;
            updateBalls(tabNumber);
            updateDrawTime(tabNumber);
        }

        // 显示开奖记录
        function showRecord() {
            alert('开奖记录功能');
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 立即设置随机阅读数
            const randomViews = Math.floor(Math.random() * 90000) + 10000; // 10000-99999
            document.getElementById('viewCount').textContent = randomViews;

            updateBalls(1); // 默认显示私彩的球
            updateDrawTime(1); // 默认显示私彩的开奖时间
            loadPostDetail();
        });
    </script>
</body>
</html>
