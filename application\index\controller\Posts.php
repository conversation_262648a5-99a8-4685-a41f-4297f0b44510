<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Post;
use app\common\model\PostCategory;

/**
 * 帖子控制器
 */
class Posts extends Frontend
{
    protected $noNeedLogin = ['index', 'detail'];
    protected $noNeedRight = '*';

    /**
     * 帖子列表页
     */
    public function index()
    {
        $this->view->assign('title', '帖子列表');
        return $this->view->fetch();
    }

    /**
     * 帖子详情页
     */
    public function detail()
    {
        $id = $this->request->get('id');
        if (!$id) {
            $this->error('参数错误');
        }

        $post = Post::with(['user', 'category'])
                   ->where('id', $id)
                   ->where('status', 'published')
                   ->find();

        if (!$post) {
            $this->error('帖子不存在');
        }

        $this->view->assign('post', $post);
        $this->view->assign('title', $post->title);
        return $this->view->fetch();
    }

    /**
     * 发布帖子页
     */
    public function create()
    {
        // 检查登录状态
        if (!$this->auth->isLogin()) {
            $this->error('请先登录', '/index/index/login');
        }

        $this->view->assign('title', '发布帖子');
        return $this->view->fetch();
    }

    /**
     * 编辑帖子页
     */
    public function edit()
    {
        // 检查登录状态
        if (!$this->auth->isLogin()) {
            $this->error('请先登录', '/index/index/login');
        }

        $id = $this->request->get('id');
        if (!$id) {
            $this->error('参数错误');
        }

        $post = Post::where('id', $id)
                   ->where('user_id', $this->auth->id)
                   ->find();

        if (!$post) {
            $this->error('帖子不存在或无权限编辑');
        }

        $this->view->assign('post', $post);
        $this->view->assign('title', '编辑帖子');
        return $this->view->fetch('edit');
    }
}
