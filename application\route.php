<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

use think\Route;

// API路由规则
Route::get('api/posts', 'api/posts/index');
Route::get('api/posts/detail/:id', 'api/posts/detail');
Route::get('api/posts/detail', 'api/posts/detail');
Route::post('api/posts/create', 'api/posts/create');
Route::post('api/posts/purchase', 'api/posts/purchase');
Route::get('api/posts/categories', 'api/posts/categories');
Route::get('api/posts/builtinIcons', 'api/posts/builtinIcons');
Route::post('api/posts/uploadIcon', 'api/posts/uploadIcon');

// 前端页面路由
Route::get('index/index/post_detail', 'index/index/post_detail');

return [
    //别名配置,别名只能是映射到控制器且访问时必须加上请求的方法
    '__alias__'   => [
    ],
    //变量规则
    '__pattern__' => [
        'id' => '\d+',
    ],
//        域名绑定到模块
//        '__domain__'  => [
//            'admin' => 'admin',
//            'api'   => 'api',
//        ],
];
