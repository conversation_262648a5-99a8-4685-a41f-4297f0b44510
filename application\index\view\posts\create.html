<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布帖子 - {$site.title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: #007bff;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
        }

        .back-btn {
            color: white;
            text-decoration: none;
            font-size: 14px;
        }

        .form-container {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }

        .form-textarea {
            resize: vertical;
            min-height: 200px;
        }

        .icon-section {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }

        .icon-type-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .icon-tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #666;
            font-size: 14px;
        }

        .icon-tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
        }

        .icon-content {
            display: none;
        }

        .icon-content.active {
            display: block;
        }

        .builtin-icons {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
        }

        .icon-item {
            text-align: center;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .icon-item:hover {
            border-color: #007bff;
        }

        .icon-item.selected {
            border-color: #007bff;
            background-color: #f0f8ff;
        }

        .icon-item img {
            width: 32px;
            height: 32px;
            margin-bottom: 5px;
        }

        .icon-item span {
            font-size: 12px;
            color: #666;
        }

        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 5px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #007bff;
            background: #f0f8ff;
        }

        .upload-area.dragover {
            border-color: #007bff;
            background: #e6f3ff;
        }

        .submit-btn {
            width: 100%;
            background: #007bff;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .submit-btn:hover {
            background: #0056b3;
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* 手机端优化 */
        @media (max-width: 768px) {
            .container {
                margin: 0;
                box-shadow: none;
            }

            .form-container {
                padding: 15px;
            }

            .builtin-icons {
                grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            }

            .icon-item {
                padding: 8px;
            }

            .icon-item img {
                width: 24px;
                height: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>发布帖子</h1>
            <a href="/" class="back-btn">返回首页</a>
        </div>

        <div class="form-container">
            <form id="postForm">
                <div class="form-group">
                    <label class="form-label">帖子标题</label>
                    <input type="text" id="title" class="form-input" placeholder="请输入帖子标题" required>
                </div>

                <div class="form-group">
                    <label class="form-label">选择分类</label>
                    <select id="category" class="form-select" required>
                        <option value="">请选择分类</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">帖子类型</label>
                    <select id="postType" class="form-select">
                        <option value="normal">普通帖</option>
                        <option value="sale">出售帖</option>
                        <option value="hot">热门帖</option>
                        <option value="expert">高手帖</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">选择图标</label>
                    <div class="icon-section">
                        <div class="icon-type-tabs">
                            <div class="icon-tab active" data-tab="builtin">内置图标</div>
                            <div class="icon-tab" data-tab="upload">上传图标</div>
                        </div>

                        <div class="icon-content active" id="builtin-content">
                            <div class="builtin-icons" id="builtinIcons">
                                <!-- 内置图标将通过JavaScript加载 -->
                            </div>
                        </div>

                        <div class="icon-content" id="upload-content">
                            <div class="upload-area" onclick="document.getElementById('iconFile').click()">
                                <input type="file" id="iconFile" accept="image/*" style="display: none;">
                                <div style="font-size: 48px; color: #ccc; margin-bottom: 10px;">📁</div>
                                <div>点击选择图标文件</div>
                                <div style="font-size: 12px; color: #999; margin-top: 5px;">支持 JPG、PNG、GIF 格式</div>
                            </div>
                            <div id="uploadPreview" style="margin-top: 10px; text-align: center;"></div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">帖子内容</label>
                    <textarea id="content" class="form-textarea" placeholder="请输入帖子内容..." required></textarea>
                </div>

                <button type="submit" class="submit-btn">发布帖子</button>
            </form>
        </div>
    </div>

    <script>
        let selectedIconType = 'builtin';
        let selectedIconValue = '';
        let categories = [];
        let builtinIcons = {};

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
            loadBuiltinIcons();
            initEventListeners();
        });

        // 加载分类列表
        async function loadCategories() {
            try {
                const response = await fetch('/api/posts/categories');
                const result = await response.json();
                if (result.code === 1) {
                    categories = result.data;
                    const categorySelect = document.getElementById('category');
                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        categorySelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载分类失败:', error);
            }
        }

        // 加载内置图标
        async function loadBuiltinIcons() {
            try {
                const response = await fetch('/api/posts/builtinIcons');
                const result = await response.json();
                if (result.code === 1) {
                    builtinIcons = result.data;
                    renderBuiltinIcons();
                }
            } catch (error) {
                console.error('加载内置图标失败:', error);
            }
        }

        // 渲染内置图标
        function renderBuiltinIcons() {
            const container = document.getElementById('builtinIcons');
            container.innerHTML = '';
            
            Object.keys(builtinIcons).forEach(key => {
                const iconDiv = document.createElement('div');
                iconDiv.className = 'icon-item';
                iconDiv.dataset.value = key;
                iconDiv.innerHTML = `
                    <img src="${builtinIcons[key]}" alt="${key}">
                    <span>${key}</span>
                `;
                iconDiv.addEventListener('click', function() {
                    selectBuiltinIcon(key, this);
                });
                container.appendChild(iconDiv);
            });
        }

        // 选择内置图标
        function selectBuiltinIcon(key, element) {
            document.querySelectorAll('.icon-item').forEach(item => {
                item.classList.remove('selected');
            });
            element.classList.add('selected');
            selectedIconType = 'builtin';
            selectedIconValue = key;
        }

        // 初始化事件监听
        function initEventListeners() {
            // 图标类型切换
            document.querySelectorAll('.icon-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabType = this.dataset.tab;
                    switchIconTab(tabType);
                });
            });

            // 文件上传
            document.getElementById('iconFile').addEventListener('change', handleFileUpload);

            // 表单提交
            document.getElementById('postForm').addEventListener('submit', handleSubmit);
        }

        // 切换图标标签
        function switchIconTab(tabType) {
            document.querySelectorAll('.icon-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.icon-content').forEach(content => {
                content.classList.remove('active');
            });

            document.querySelector(`[data-tab="${tabType}"]`).classList.add('active');
            document.getElementById(`${tabType}-content`).classList.add('active');
        }

        // 处理文件上传
        async function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/api/posts/uploadIcon', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                
                if (result.code === 1) {
                    selectedIconType = 'upload';
                    selectedIconValue = result.data.url;
                    
                    // 显示预览
                    const preview = document.getElementById('uploadPreview');
                    preview.innerHTML = `<img src="${result.data.url}" style="width: 64px; height: 64px;">`;
                } else {
                    alert(result.msg || '上传失败');
                }
            } catch (error) {
                console.error('上传失败:', error);
                alert('上传失败');
            }
        }

        // 处理表单提交
        async function handleSubmit(event) {
            event.preventDefault();

            const title = document.getElementById('title').value.trim();
            const category = document.getElementById('category').value;
            const postType = document.getElementById('postType').value;
            const content = document.getElementById('content').value.trim();

            if (!title || !category || !content) {
                alert('请填写完整信息');
                return;
            }

            const submitBtn = document.querySelector('.submit-btn');
            submitBtn.disabled = true;
            submitBtn.textContent = '发布中...';

            try {
                const response = await fetch('/api/posts/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        content: content,
                        category_id: category,
                        post_type: postType,
                        icon_type: selectedIconType,
                        icon_path: selectedIconValue
                    })
                });

                const result = await response.json();
                
                if (result.code === 1) {
                    alert('发布成功！');
                    window.location.href = `/index/posts/detail?id=${result.data.id}`;
                } else {
                    alert(result.msg || '发布失败');
                }
            } catch (error) {
                console.error('发布失败:', error);
                alert('发布失败');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '发布帖子';
            }
        }
    </script>
</body>
</html>
