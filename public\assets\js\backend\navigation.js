define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'navigation/index',
                    add_url: 'navigation/add',
                    edit_url: 'navigation/edit',
                    del_url: 'navigation/del',
                    multi_url: 'navigation/multi',
                    dragsort_url: 'ajax/weigh',
                    table: 'navigation',
                }
            });

            var table = $("#table");
            var tableOptions = {
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'sort',
                pagination: true,
                commonSearch: true,
                search: true,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), width: 60},
                        {field: 'title', title: __('Title'), align: 'left'},
                        {field: 'image', title: __('Image'), width: 120, operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'url', title: __('Url'), align: 'left'},
                        {field: 'target', title: __('Target'), width: 100, searchList: {"_self":__("Current window"),"_blank":__("New window")}, formatter: Table.api.formatter.normal},
                        {field: 'sort', title: __('Sort'), width: 80},
                        {field: 'status', title: __('Status'), width: 100, searchList: {"normal":__("Normal"),"hidden":__("Hidden")}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), width: 160, operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), width: 160, operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            };
            // 初始化表格
            table.bootstrapTable(tableOptions);

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
