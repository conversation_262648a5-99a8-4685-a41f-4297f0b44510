<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .nav-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        .nav-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .nav-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .error {
            color: red;
            padding: 10px;
            background: #ffe6e6;
            border-radius: 5px;
        }
        .success {
            color: green;
            padding: 10px;
            background: #e6ffe6;
            border-radius: 5px;
        }
        .loading {
            color: #666;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>导航功能测试页面</h1>
    
    <div class="test-section">
        <h2>API测试</h2>
        <button onclick="testAPI()">测试导航API</button>
        <div id="apiResult" class="loading">点击按钮测试API</div>
    </div>

    <div class="test-section">
        <h2>导航显示测试</h2>
        <div id="navigationDisplay">
            <div class="loading">正在加载导航...</div>
        </div>
    </div>

    <script>
        // 测试API
        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="loading">正在测试API...</div>';
            
            try {
                const response = await fetch('/api/navigation/index');
                const result = await response.json();
                
                if (result.code === 1) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>API测试成功！</strong><br>
                            返回数据数量: ${result.data.length}<br>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                    renderNavigation(result.data);
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>API返回错误：</strong><br>
                            ${result.msg || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>API请求失败：</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }

        // 渲染导航
        function renderNavigation(navigationData) {
            const displayDiv = document.getElementById('navigationDisplay');
            
            if (!navigationData || navigationData.length === 0) {
                displayDiv.innerHTML = '<div class="error">没有导航数据</div>';
                return;
            }

            let html = '<h3>导航列表：</h3>';
            navigationData.forEach(nav => {
                const iconHtml = nav.image ? 
                    `<img src="${nav.image}" alt="${nav.title}" onerror="this.style.display='none'">` : 
                    `<span>${nav.title.charAt(0)}</span>`;
                
                html += `
                    <a href="${nav.url}" class="nav-item" target="${nav.target}">
                        <div class="nav-icon">${iconHtml}</div>
                        <div>
                            <strong>${nav.title}</strong><br>
                            <small>URL: ${nav.url} | 打开方式: ${nav.target}</small>
                        </div>
                    </a>
                `;
            });

            displayDiv.innerHTML = html;
        }

        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            testAPI();
        });
    </script>
</body>
</html>
