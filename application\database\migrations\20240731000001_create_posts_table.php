<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreatePostsTable extends Migrator
{
    /**
     * 创建帖子表
     */
    public function change()
    {
        $table = $this->table('posts', ['id' => false, 'primary_key' => ['id']]);
        $table->addColumn('id', 'integer', ['identity' => true, 'signed' => false, 'comment' => '帖子ID'])
              ->addColumn('user_id', 'integer', ['signed' => false, 'comment' => '用户ID'])
              ->addColumn('category_id', 'integer', ['signed' => false, 'default' => 0, 'comment' => '分类ID'])
              ->addColumn('title', 'string', ['limit' => 255, 'comment' => '帖子标题'])
              ->addColumn('content', 'text', ['comment' => '帖子内容'])
              ->addColumn('post_type', 'string', ['limit' => 50, 'default' => 'normal', 'comment' => '帖子类型：normal普通,sale出售,hot热门,expert高手'])
              ->addColumn('icon_type', 'string', ['limit' => 20, 'default' => 'default', 'comment' => '图标类型：default默认,upload上传,builtin内置'])
              ->addColumn('icon_path', 'string', ['limit' => 255, 'null' => true, 'comment' => '图标路径'])
              ->addColumn('views', 'integer', ['signed' => false, 'default' => 0, 'comment' => '浏览次数'])
              ->addColumn('replies', 'integer', ['signed' => false, 'default' => 0, 'comment' => '回复数'])
              ->addColumn('likes', 'integer', ['signed' => false, 'default' => 0, 'comment' => '点赞数'])
              ->addColumn('is_top', 'boolean', ['default' => false, 'comment' => '是否置顶'])
              ->addColumn('is_hot', 'boolean', ['default' => false, 'comment' => '是否热门'])
              ->addColumn('status', 'string', ['limit' => 20, 'default' => 'published', 'comment' => '状态：draft草稿,published已发布,hidden隐藏,deleted已删除'])
              ->addColumn('create_time', 'datetime', ['comment' => '创建时间'])
              ->addColumn('update_time', 'datetime', ['comment' => '更新时间'])
              ->addIndex(['user_id'])
              ->addIndex(['category_id'])
              ->addIndex(['post_type'])
              ->addIndex(['status'])
              ->addIndex(['create_time'])
              ->create();
    }
}
