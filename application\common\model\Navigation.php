<?php

namespace app\common\model;

use think\Model;

/**
 * 导航链接模型
 */
class Navigation extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [
        'status_text',
        'target_text',
        'image_text'
    ];

    protected static function init()
    {
        self::afterInsert(function ($row) {
            if (!$row['sort']) {
                $row->save(['sort' => $row['id']]);
            }
        });
    }

    /**
     * 获取状态列表
     * @return array
     */
    public static function getStatusList()
    {
        return [
            'normal' => __('Normal'),
            'hidden' => __('Hidden')
        ];
    }

    /**
     * 获取打开方式列表
     * @return array
     */
    public static function getTargetList()
    {
        return [
            '_self' => __('Current window'),
            '_blank' => __('New window')
        ];
    }

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return mixed
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 获取打开方式文本
     * @param $value
     * @param $data
     * @return mixed
     */
    public function getTargetTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['target']) ? $data['target'] : '');
        $list = $this->getTargetList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 获取图片文本
     * @param $value
     * @param $data
     * @return mixed
     */
    public function getImageTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['image']) ? $data['image'] : '');
        return $value ? cdnurl($value, true) : '';
    }

    /**
     * 获取启用的导航列表
     * @return array
     */
    public static function getActiveList()
    {
        return self::where('status', 'normal')
            ->order('sort', 'asc')
            ->select();
    }
}
